export function mergeConfigWithFallback<T extends object>(userConfig: Partial<T> | null | undefined, defaultConfig: T): T {
    if (!userConfig) return { ...defaultConfig }
    
    console.log("Default: ",defaultConfig)

    const result = { ...defaultConfig }


    Object.keys(defaultConfig).forEach((key) => {
        const value = (userConfig as any)[key]
        if (!isEmpty(value)) {
            ;(result[key] as any) = value
        }
    })

    return result
}

const isEmpty = (value: any): boolean => {
    if (value === null || value === undefined) return true
    if (typeof value === 'string' && value.trim() === '') return false
    if (typeof value === 'object' && !Array.isArray(value)) {
        return Object.keys(value).length === 0
    }
    return false
}
